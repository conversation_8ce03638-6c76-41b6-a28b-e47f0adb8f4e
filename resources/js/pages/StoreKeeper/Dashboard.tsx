import { useEffect } from 'react';
import { <PERSON>, <PERSON>, usePage } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON><PERSON>, pieChartColors } from '@/components/ui/pie-chart';
import {
    Package,
    AlertTriangle,
    CheckCircle,
    Clock,
    TrendingUp,
    Plus,
    ClipboardCheck,
    Activity,
    Send,
    FileText,
    Settings,
    Eye,
    ArrowUpDown,
    BarChart3,
    User
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface DashboardStats {
    // Core metrics with context
    pending_approvals: number;
    overdue_approvals: number;
    avg_approval_time_hours: number;
    longest_pending_hours: number;

    // Inventory health with severity levels
    inventory_items: number;
    low_stock_alerts: number;
    critical_stock_alerts: number;
    out_of_stock_items: number;

    // Processing efficiency
    pending_issues: number;
    partial_issues: number;
    ready_to_issue: number;

    // Activity trends
    todays_transactions: number;
    weekly_transactions: number;
    weekly_requisition_trend: number;

    // Legacy fields (keeping for compatibility)
    categories_count: number;
    total_inventory_value: number;

    // Deprecated fields (for backward compatibility)
    my_requisitions?: number;
    recent_approvals?: number;
    awaiting_fulfillment?: number;
    avg_approval_time?: number;
}

interface PendingApproval {
    id: number;
    purpose: string;
    status: string;
    requested_at: string;
    requester: {
        id: number;
        name: string;
        email: string;
    } | null;
    department: {
        id: number;
        name: string;
    } | null;
    items_count: number;
    items_preview: Array<{
        name: string;
        sku: string;
        quantity_requested: number;
    }>;
}

interface InventoryAlert {
    id: number;
    sku: string;
    name: string;
    quantity_on_hand: number;
    reorder_level: number;
    unit_of_measure: string;
    branch: {
        id: number;
        name: string;
    } | null;
    is_out_of_stock: boolean;
    stock_percentage: number;
}

interface ActivityItem {
    type: 'approval' | 'inventory';
    action: string;
    description: string;
    user?: string;
    quantity?: number;
    timestamp: string;
    link?: string;
}

interface TopRequestedItem {
    inventory_item_id: number;
    name: string;
    sku: string;
    unit_of_measure: string;
    total_request_count: number;
    approved_count: number;
    total_quantity: number;
    approval_rate: number;
    status_breakdown: {
        pending_approval: number;
        approved: number;
        rejected: number;
        issued: number;
        partially_issued: number;
        returned_for_revision: number;
    };
}

interface StockVsDemandItem {
    inventory_item_id: number;
    name: string;
    sku: string;
    unit_of_measure: string;
    current_stock: number;
    monthly_demand: number;
    request_count: number;
    stock_status: 'healthy' | 'low' | 'critical' | 'out_of_stock';
    days_remaining: number;
    reorder_level: number;
}

interface StoreKeeperDashboardPageProps {
    stats: DashboardStats;
    pendingApprovals: PendingApproval[];
    inventoryAlerts: InventoryAlert[];
    recentActivity: ActivityItem[];
    topRequestedItems: TopRequestedItem[];
    stockVsDemandData: StockVsDemandItem[];
    user: {
        id: number;
        name: string;
        email: string;
        permissions: string[];
    };
    flash?: {
        success?: string;
        error?: string;
    };
    [key: string]: unknown;
}

export default function StoreKeeperDashboard() {
    const { stats, pendingApprovals, inventoryAlerts, recentActivity, topRequestedItems, stockVsDemandData, flash } =
        usePage<StoreKeeperDashboardPageProps>().props;

    // Handle flash messages
    useEffect(() => {
        if (flash?.success) {
            window.showToast?.({
                title: 'Success',
                message: flash.success,
                type: 'success'
            });
        }
        if (flash?.error) {
            window.showToast?.({
                title: 'Error',
                message: flash.error,
                type: 'error'
            });
        }
    }, [flash]);

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Store Keeper Dashboard', href: '/store-keeper/dashboard' },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Store Keeper Dashboard" />
            
            <div className="bg-background/90 flex h-full flex-1 flex-col gap-4 sm:gap-6 p-3 sm:p-4 lg:p-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                    <div className="min-w-0 flex-1">
                        <h1 className="text-2xl sm:text-3xl font-bold text-foreground truncate">
                            Store Keeper Dashboard
                        </h1>
                        <p className="text-sm sm:text-base text-muted-foreground mt-1">
                            Manage inventory, approve requisitions, and monitor stock levels
                        </p>
                    </div>
                    <div className="flex flex-row items-center gap-2 sm:gap-3 w-full sm:w-auto">
                        <Button className="flex-1 sm:flex-none sm:w-auto text-sm" asChild>
                            <Link href="/store-requisitions/create">
                                <Plus className="h-4 w-4 mr-1 sm:mr-2 flex-shrink-0" />
                                <span className="truncate">New</span>
                                <span className="hidden sm:inline ml-1">Requisition</span>
                            </Link>
                        </Button>
                        <Button variant="outline" className="flex-1 sm:flex-none sm:w-auto text-sm" asChild>
                            <Link href="/inventory">
                                <Package className="h-4 w-4 mr-1 sm:mr-2 flex-shrink-0" />
                                <span className="truncate">Inventory</span>
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Statistics Cards */}
                <div className="grid grid-cols-2 gap-3 sm:gap-4 lg:grid-cols-4">
                    {/* Pending Approvals with Context */}
                    <Link href="/store-requisitions/approvals" className="block h-full">
                        <Card className="p-3 sm:p-4 shadow-md transition-shadow hover:shadow-lg h-full hover:bg-muted/30">
                            <div className="flex items-center justify-between h-full min-h-[80px] sm:min-h-[100px]">
                                <div className="flex flex-col flex-1 min-w-0">
                                    <div className="text-foreground text-lg sm:text-xl lg:text-2xl font-bold">
                                        {stats.pending_approvals}
                                    </div>
                                    <div className="text-muted-foreground text-xs sm:text-sm font-medium truncate">
                                        Pending Approvals
                                    </div>
                                    {(stats.overdue_approvals > 0 || stats.avg_approval_time_hours > 0) && (
                                        <div className="text-xs text-muted-foreground mt-1 truncate">
                                            {stats.overdue_approvals > 0 && (
                                                <span className="text-destructive font-medium">
                                                    {stats.overdue_approvals} overdue
                                                </span>
                                            )}
                                            {stats.overdue_approvals > 0 && stats.avg_approval_time_hours > 0 && (
                                                <span className="hidden sm:inline">, </span>
                                            )}
                                            {stats.avg_approval_time_hours > 0 && (
                                                <span className={stats.overdue_approvals > 0 ? "hidden sm:inline" : ""}>
                                                    avg: {stats.avg_approval_time_hours}h
                                                </span>
                                            )}
                                        </div>
                                    )}
                                </div>
                                <div className={`rounded-full p-2 sm:p-3 flex-shrink-0 ml-2 ${
                                    stats.overdue_approvals > 0 ? 'bg-destructive/10' : 'bg-warning/10'
                                }`}>
                                    <Clock className={`h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 ${
                                        stats.overdue_approvals > 0 ? 'text-destructive' : 'text-warning'
                                    }`} />
                                </div>
                            </div>
                        </Card>
                    </Link>

                    {/* Inventory Items */}
                    <Link href="/inventory" className="block h-full">
                        <Card className="p-3 sm:p-4 shadow-md transition-shadow hover:shadow-lg h-full hover:bg-muted/30">
                            <div className="flex items-center justify-between h-full min-h-[80px] sm:min-h-[100px]">
                                <div className="flex flex-col flex-1 min-w-0">
                                    <div className="text-foreground text-lg sm:text-xl lg:text-2xl font-bold">
                                        {stats.inventory_items}
                                    </div>
                                    <div className="text-muted-foreground text-xs sm:text-sm font-medium truncate">
                                        Inventory Items
                                    </div>
                                </div>
                                <div className="bg-primary/10 rounded-full p-2 sm:p-3 flex-shrink-0 ml-2">
                                    <Package className="text-primary h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6" />
                                </div>
                            </div>
                        </Card>
                    </Link>

                    {/* Stock Alerts with Severity */}
                    <Link href="/inventory/low-stock" className="block h-full">
                        <Card className="p-3 sm:p-4 shadow-md transition-shadow hover:shadow-lg h-full hover:bg-muted/30">
                            <div className="flex items-center justify-between h-full min-h-[80px] sm:min-h-[100px]">
                                <div className="flex flex-col flex-1 min-w-0">
                                    <div className="text-foreground text-lg sm:text-xl lg:text-2xl font-bold">
                                        {stats.low_stock_alerts}
                                    </div>
                                    <div className="text-muted-foreground text-xs sm:text-sm font-medium truncate">
                                        Stock Alerts
                                    </div>
                                    {(stats.critical_stock_alerts > 0 || stats.out_of_stock_items > 0) && (
                                        <div className="text-xs text-muted-foreground mt-1">
                                            {stats.out_of_stock_items > 0 && (
                                                <div className="text-destructive font-medium truncate">
                                                    {stats.out_of_stock_items} out of stock
                                                </div>
                                            )}
                                            {stats.critical_stock_alerts > 0 && (
                                                <div className="text-orange-600 font-medium truncate">
                                                    {stats.critical_stock_alerts} critical
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                                <div className={`rounded-full p-2 sm:p-3 flex-shrink-0 ml-2 ${
                                    stats.out_of_stock_items > 0 ? 'bg-destructive/10' :
                                    stats.critical_stock_alerts > 0 ? 'bg-orange-100' : 'bg-yellow-100'
                                }`}>
                                    <AlertTriangle className={`h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 ${
                                        stats.out_of_stock_items > 0 ? 'text-destructive' :
                                        stats.critical_stock_alerts > 0 ? 'text-orange-600' : 'text-yellow-600'
                                    }`} />
                                </div>
                            </div>
                        </Card>
                    </Link>

                    {/* Ready to Issue */}
                    <Link href="/store-requisitions/issue" className="block h-full">
                        <Card className="p-3 sm:p-4 shadow-md transition-shadow hover:shadow-lg h-full hover:bg-muted/30">
                            <div className="flex items-center justify-between h-full min-h-[80px] sm:min-h-[100px]">
                                <div className="flex flex-col flex-1 min-w-0">
                                    <div className="text-foreground text-lg sm:text-xl lg:text-2xl font-bold">
                                        {stats.ready_to_issue}
                                    </div>
                                    <div className="text-muted-foreground text-xs sm:text-sm font-medium truncate">
                                        Ready to Issue
                                    </div>
                                    {(stats.pending_issues > 0 || stats.partial_issues > 0) && (
                                        <div className="text-xs text-muted-foreground mt-1 truncate">
                                            {stats.pending_issues > 0 && (
                                                <span>{stats.pending_issues} new</span>
                                            )}
                                            {stats.pending_issues > 0 && stats.partial_issues > 0 && (
                                                <span className="hidden sm:inline">, </span>
                                            )}
                                            {stats.partial_issues > 0 && (
                                                <span className={stats.pending_issues > 0 ? "hidden sm:inline" : ""}>
                                                    {stats.partial_issues} partial
                                                </span>
                                            )}
                                        </div>
                                    )}
                                </div>
                                <div className="bg-primary/10 rounded-full p-2 sm:p-3 flex-shrink-0 ml-2">
                                    <Send className="text-primary h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6" />
                                </div>
                            </div>
                        </Card>
                    </Link>



                </div>

                {/* Stock vs Demand and Top Requested Items Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                    {/* Stock vs Demand Chart */}
                    <Card className="h-full">
                        <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0 pb-3 sm:pb-2">
                            <CardTitle className="text-base sm:text-lg font-semibold flex items-center gap-2">
                                <BarChart3 className="h-4 w-4 sm:h-5 sm:w-5 text-primary flex-shrink-0" />
                                <span className="truncate">Top Items - Stock vs Demand</span>
                            </CardTitle>
                            <Button variant="outline" size="sm" className="text-xs sm:text-sm px-2 sm:px-3" asChild>
                                <Link href="/inventory">
                                    View All
                                </Link>
                            </Button>
                        </CardHeader>
                        <CardContent className="p-3 sm:p-6">
                            {stockVsDemandData && stockVsDemandData.length > 0 ? (
                                <div className="space-y-3 sm:space-y-4">
                                    {stockVsDemandData.map((item, index) => {
                                        const maxValue = Math.max(
                                            ...stockVsDemandData.map(i => Math.max(i.current_stock, i.monthly_demand))
                                        );
                                        const stockPercentage = maxValue > 0 ? (item.current_stock / maxValue) * 100 : 0;
                                        const demandPercentage = maxValue > 0 ? (item.monthly_demand / maxValue) * 100 : 0;

                                        const getStockColor = (status: string) => {
                                            switch (status) {
                                                case 'out_of_stock': return 'bg-red-500';
                                                case 'critical': return 'bg-orange-500';
                                                case 'low': return 'bg-yellow-500';
                                                default: return 'bg-green-500';
                                            }
                                        };

                                        return (
                                            <div key={item.inventory_item_id} className="space-y-2">
                                                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
                                                    <span className="font-medium text-sm sm:text-base truncate flex-1">{item.name}</span>
                                                    <div className="flex items-center gap-1 sm:gap-2 text-xs text-muted-foreground flex-shrink-0">
                                                        <span className="whitespace-nowrap">{item.current_stock} {item.unit_of_measure}</span>
                                                        <span className="hidden sm:inline">•</span>
                                                        <span className="whitespace-nowrap">{item.monthly_demand} req'd</span>
                                                    </div>
                                                </div>
                                                <div className="space-y-2">
                                                    {/* Current Stock Bar */}
                                                    <div className="flex items-center gap-2 sm:gap-3">
                                                        <span className="text-xs text-muted-foreground w-12 sm:w-14 flex-shrink-0">Stock:</span>
                                                        <div className="flex-1 bg-muted rounded-full h-2 sm:h-3">
                                                            <div
                                                                className={`h-2 sm:h-3 rounded-full transition-all duration-300 ${getStockColor(item.stock_status)}`}
                                                                style={{ width: `${stockPercentage}%` }}
                                                            />
                                                        </div>
                                                    </div>
                                                    {/* Monthly Demand Bar */}
                                                    <div className="flex items-center gap-2 sm:gap-3">
                                                        <span className="text-xs text-muted-foreground w-12 sm:w-14 flex-shrink-0">Demand:</span>
                                                        <div className="flex-1 bg-muted rounded-full h-2 sm:h-3">
                                                            <div
                                                                className="h-2 sm:h-3 rounded-full bg-blue-500 transition-all duration-300"
                                                                style={{ width: `${demandPercentage}%` }}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                                {item.days_remaining < 30 && (
                                                    <div className="text-xs sm:text-sm text-orange-600 font-medium">
                                                        ~{item.days_remaining} days remaining
                                                    </div>
                                                )}
                                            </div>
                                        );
                                    })}
                                </div>
                            ) : (
                                <div className="text-center py-6 sm:py-8">
                                    <BarChart3 className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground mx-auto mb-3 sm:mb-4" />
                                    <p className="text-sm sm:text-base text-muted-foreground">No demand data available</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Top Requested Items Card */}
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-lg font-semibold flex items-center gap-2">
                                <BarChart3 className="h-5 w-5 text-success" />
                                Top Requested Items
                            </CardTitle>
                            <Button variant="outline" size="sm" asChild>
                                <Link href="/store-requisitions">
                                    View All
                                </Link>
                            </Button>
                        </CardHeader>
                        <CardContent>
                            {topRequestedItems && topRequestedItems.length > 0 ? (
                                <div className="space-y-3">
                                    {topRequestedItems.map((item) => {
                                        const maxCount = topRequestedItems[0]?.total_request_count || 1;
                                        const percentage = (item.total_request_count / maxCount) * 100;

                                        return (
                                            <div key={item.inventory_item_id} className="space-y-2">
                                                <div className="flex items-center justify-between text-sm">
                                                    <div className="flex-1 min-w-0">
                                                        <div className="font-medium truncate">{item.name}</div>
                                                        <div className="text-xs text-muted-foreground flex items-center gap-2">
                                                            <span>{item.sku}</span>
                                                            <span>•</span>
                                                            <span className={`font-medium ${
                                                                item.approval_rate >= 80 ? 'text-green-600' :
                                                                item.approval_rate >= 60 ? 'text-yellow-600' : 'text-red-600'
                                                            }`}>
                                                                {item.approval_rate}% approved
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div className="text-right ml-2">
                                                        <div className="font-semibold">{item.total_request_count}</div>
                                                        <div className="text-xs text-muted-foreground">requests</div>
                                                    </div>
                                                </div>
                                                <div className="w-full bg-muted rounded-full h-2">
                                                    <div
                                                        className="bg-primary h-2 rounded-full transition-all duration-300"
                                                        style={{ width: `${percentage}%` }}
                                                    />
                                                </div>
                                            </div>
                                        );
                                    })}

                                    {/* View All Button */}
                                    <div className="pt-2 border-t border-border/50">
                                        <Button variant="outline" size="sm" className="text-xs px-2 sm:px-3" asChild>
                                            <Link href="/store-requisitions/analytics">
                                                <span className="sm:hidden">Analytics</span>
                                                <span className="hidden sm:inline">View Detailed Analytics</span>
                                            </Link>
                                        </Button>
                                    </div>
                                </div>
                            ) : (
                                <div className="text-center py-8 text-muted-foreground">
                                    <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                    <p className="text-sm">No requisition data available</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Main Content Grid */}
                <div className="grid grid-cols-1 gap-4 sm:gap-6">
                    {/* Critical Actions Needed Card */}
                    <Card className="h-[400px] sm:h-[480px] flex flex-col">
                        <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0 pb-3 sm:pb-2 flex-shrink-0">
                            <CardTitle className="text-base sm:text-lg font-semibold flex items-center gap-2">
                                <AlertTriangle className="h-4 w-4 sm:h-5 sm:w-5 text-orange-600 flex-shrink-0" />
                                <span className="truncate">Critical Actions Needed</span>
                            </CardTitle>
                            <Button variant="outline" size="sm" className="text-xs sm:text-sm px-2 sm:px-3" asChild>
                                <Link href="/inventory">
                                    View All
                                </Link>
                            </Button>
                        </CardHeader>
                        <CardContent className="flex-1 overflow-y-auto p-3 sm:p-6">
                            <div className="space-y-3 sm:space-y-4">
                                {/* Only show the most critical items that need immediate attention */}
                                {stats.out_of_stock_items > 0 && (
                                    <Link href="/inventory/low-stock" className="block">
                                        <div className="p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center gap-2 min-w-0 flex-1">
                                                    <div className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0"></div>
                                                    <span className="font-medium text-red-800 text-sm sm:text-base truncate">Out of Stock Items</span>
                                                </div>
                                                <Badge variant="destructive" className="text-xs sm:text-sm flex-shrink-0">{stats.out_of_stock_items}</Badge>
                                            </div>
                                            <p className="text-xs sm:text-sm text-red-600 mt-1">Immediate restocking required</p>
                                        </div>
                                    </Link>
                                )}

                                {/* Only show overdue approvals if they exist */}
                                {stats.overdue_approvals > 0 && (
                                    <Link href="/store-requisitions/approvals" className="block">
                                        <div className="p-3 sm:p-4 bg-yellow-50 border border-yellow-200 rounded-lg hover:bg-yellow-100 transition-colors">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center gap-2 min-w-0 flex-1">
                                                    <div className="w-2 h-2 bg-yellow-500 rounded-full flex-shrink-0"></div>
                                                    <span className="font-medium text-yellow-800 text-sm sm:text-base truncate">Overdue Approvals</span>
                                                </div>
                                                <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300 text-xs sm:text-sm flex-shrink-0">{stats.overdue_approvals}</Badge>
                                            </div>
                                            <p className="text-xs sm:text-sm text-yellow-600 mt-1">Pending for more than 24 hours</p>
                                        </div>
                                    </Link>
                                )}

                                {/* Performance Summary */}
                                <div className="pt-3 sm:pt-4 border-t border-border">
                                    <h4 className="font-medium text-sm sm:text-base mb-3">Performance Summary</h4>
                                    <div className="space-y-2 text-xs sm:text-sm">
                                        <div className="flex items-center justify-between">
                                            <span className="text-muted-foreground truncate flex-1 mr-2">Avg Approval Time</span>
                                            <span className="font-medium flex-shrink-0">{stats.avg_approval_time_hours}h</span>
                                        </div>
                                        {stats.longest_pending_hours > 0 && (
                                            <div className="flex items-center justify-between">
                                                <span className="text-muted-foreground truncate flex-1 mr-2">Longest Pending</span>
                                                <span className="font-medium flex-shrink-0">{stats.longest_pending_hours}h</span>
                                            </div>
                                        )}
                                        <div className="flex items-center justify-between">
                                            <span className="text-muted-foreground truncate flex-1 mr-2">Weekly Trend</span>
                                            <span className={`font-medium flex-shrink-0 ${
                                                stats.weekly_requisition_trend > 0 ? 'text-green-600' :
                                                stats.weekly_requisition_trend < 0 ? 'text-red-600' : 'text-muted-foreground'
                                            }`}>
                                                {stats.weekly_requisition_trend > 0 ? '↑' : stats.weekly_requisition_trend < 0 ? '↓' : ''}
                                                {Math.abs(stats.weekly_requisition_trend)}%
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* All Good State */}
                                {stats.out_of_stock_items === 0 && stats.overdue_approvals === 0 && (
                                    <div className="text-center py-6 sm:py-8">
                                        <CheckCircle className="h-8 w-8 sm:h-12 sm:w-12 text-green-500 mx-auto mb-3 sm:mb-4" />
                                        <p className="text-green-600 font-medium text-sm sm:text-base">No critical issues!</p>
                                        <p className="text-xs sm:text-sm text-muted-foreground mt-1">All urgent matters are handled</p>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                    {/* Pending Approvals Widget */}
                    <Card className="h-[400px] sm:h-[480px] flex flex-col">
                        <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0 pb-3 sm:pb-2 flex-shrink-0">
                            <CardTitle className="text-base sm:text-lg font-semibold flex items-center gap-2">
                                <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-warning flex-shrink-0" />
                                <span className="truncate">Pending Approvals</span>
                            </CardTitle>
                            {pendingApprovals.length > 0 && (
                                <Button variant="outline" size="sm" className="text-xs sm:text-sm px-2 sm:px-3" asChild>
                                    <Link href="/store-requisitions/approvals">
                                        View All
                                    </Link>
                                </Button>
                            )}
                        </CardHeader>
                        <CardContent className="flex-1 overflow-y-auto p-3 sm:p-6">
                            {pendingApprovals.length === 0 ? (
                                <div className="text-center py-6 sm:py-8">
                                    <CheckCircle className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground mx-auto mb-3 sm:mb-4" />
                                    <p className="text-sm sm:text-base text-muted-foreground">No pending approvals</p>
                                </div>
                            ) : (
                                <div className="space-y-3">
                                    {pendingApprovals.slice(0, 3).map((approval) => (
                                        <div key={approval.id} className="p-4 bg-muted/50 rounded-lg border hover:bg-muted/70 transition-colors">
                                            {/* Header with requisition number and status */}
                                            <div className="flex items-center justify-between mb-3">
                                                <div className="flex items-center gap-2">
                                                    <span className="font-bold text-lg text-primary">#{approval.id}</span>                                    
                                                </div>
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/store-requisitions/${approval.id}`}>
                                                        Review
                                                    </Link>
                                                </Button>
                                            </div>

                                            {/* Requisition title/purpose */}
                                            <div className="mb-3">
                                                <h4 className="font-medium text-foreground leading-tight mb-1">
                                                    {approval.purpose}
                                                </h4>
                                            </div>

                                            {/* Details row */}
                                            <div className="flex items-center justify-between text-sm">
                                                <div className="flex items-center gap-4 text-muted-foreground">
                                                    <div className="flex items-center gap-1">
                                                        <Package className="h-4 w-4" />
                                                        <span className="font-medium text-foreground">{approval.items_count}</span>
                                                        <span>items</span>
                                                    </div>
                                                    <div className="flex items-center gap-1">
                                                        <User className="h-4 w-4" />
                                                        <span>{approval.requester?.name}</span>
                                                    </div>
                                                </div>
                                                <div className="text-muted-foreground">
                                                    {approval.department?.name}
                                                </div>
                                            </div>

                                            {/* Items preview */}
                                            {approval.items_preview && approval.items_preview.length > 0 && (
                                                <div className="mt-3 pt-3 border-t border-border/50">
                                                    <div className="text-xs text-muted-foreground mb-1">Items requested:</div>
                                                    <div className="flex flex-wrap gap-1">
                                                        {approval.items_preview.slice(0, 2).map((item, index) => (
                                                            <span key={index} className="text-xs bg-background px-2 py-1 rounded border">
                                                                {item.quantity_requested}× {item.name}
                                                            </span>
                                                        ))}
                                                        {approval.items_preview.length > 2 && (
                                                            <span className="text-xs text-muted-foreground px-2 py-1">
                                                                +{approval.items_preview.length - 2} more
                                                            </span>
                                                        )}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                    </Card>






                </div>

                {/* Recent Activity */}
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-lg font-semibold flex items-center gap-2">
                            <Activity className="h-5 w-5 text-success" />
                            Recent Activity
                        </CardTitle>
                        <Button variant="outline" size="sm" asChild>
                            <Link href="/activity">
                                View All
                            </Link>
                        </Button>
                    </CardHeader>
                    <CardContent>
                        {recentActivity.length === 0 ? (
                            <div className="text-center py-8">
                                <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <p className="text-muted-foreground">No recent activity</p>
                            </div>
                        ) : (
                            <div className="space-y-3">
                                {recentActivity.slice(0, 5).map((activity, index) => {
                                    const getActivityIcon = () => {
                                        const activityType = activity.type as string;
                                        const action = activity.action?.toLowerCase() || '';

                                        switch (activityType) {
                                            case 'approval':
                                                if (action === 'approved') {
                                                    return (
                                                        <div title="Approval completed">
                                                            <CheckCircle
                                                                className="h-4 w-4 text-success"
                                                                aria-label="Approved"
                                                            />
                                                        </div>
                                                    );
                                                } else if (action === 'rejected') {
                                                    return (
                                                        <div title="Approval rejected">
                                                            <AlertTriangle
                                                                className="h-4 w-4 text-destructive"
                                                                aria-label="Rejected"
                                                            />
                                                        </div>
                                                    );
                                                } else {
                                                    return (
                                                        <div title="Awaiting approval">
                                                            <Clock
                                                                className="h-4 w-4 text-warning"
                                                                aria-label="Pending approval"
                                                            />
                                                        </div>
                                                    );
                                                }
                                            case 'requisition':
                                                return (
                                                    <div title="Store requisition activity">
                                                        <ClipboardCheck
                                                            className="h-4 w-4 text-primary"
                                                            aria-label="Requisition activity"
                                                        />
                                                    </div>
                                                );
                                            case 'inventory':
                                                return (
                                                    <div title="Inventory management activity">
                                                        <Package
                                                            className="h-4 w-4 text-info"
                                                            aria-label="Inventory activity"
                                                        />
                                                    </div>
                                                );
                                            default:
                                                return (
                                                    <div title="System activity">
                                                        <Activity
                                                            className="h-4 w-4 text-muted-foreground"
                                                            aria-label="General activity"
                                                        />
                                                    </div>
                                                );
                                        }
                                    };

                                    const getTimeAgo = (timestamp: string) => {
                                        const now = new Date();
                                        const activityTime = new Date(timestamp);
                                        const diffInMinutes = Math.floor((now.getTime() - activityTime.getTime()) / (1000 * 60));

                                        if (diffInMinutes < 1) return 'Just now';
                                        if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
                                        if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
                                        return `${Math.floor(diffInMinutes / 1440)}d ago`;
                                    };

                                    return (
                                        <div key={index} className="flex items-start gap-3 p-3 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors">
                                            <div className="mt-0.5">
                                                {getActivityIcon()}
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <p className="text-sm">
                                                    <span className="font-medium capitalize">{activity.action}</span>
                                                    {' '}{activity.description}
                                                    {activity.quantity && (
                                                        <span className="text-muted-foreground">
                                                            {' '}({activity.quantity > 0 ? '+' : ''}{activity.quantity})
                                                        </span>
                                                    )}
                                                </p>
                                                <div className="flex items-center gap-2 mt-1">
                                                    {activity.user && (
                                                        <span className="text-xs text-muted-foreground">
                                                            by {activity.user}
                                                        </span>
                                                    )}
                                                    <span className="text-xs text-muted-foreground">
                                                        {getTimeAgo(activity.timestamp)}
                                                    </span>
                                                </div>
                                            </div>
                                            {activity.link && (
                                                <Button variant="ghost" size="sm" asChild>
                                                    <Link href={activity.link}>
                                                        <Eye className="h-3 w-3" />
                                                    </Link>
                                                </Button>
                                            )}
                                        </div>
                                    );
                                })}
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Quick Actions Section - Moved to Bottom */}
                <Card>
                    <CardHeader>
                        <CardTitle>Quick Actions</CardTitle>
                        <CardDescription>Common store management tasks</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-2 gap-3 sm:gap-4 md:grid-cols-3 lg:grid-cols-4">
                            <Button variant="outline" className="flex h-20 sm:h-24 flex-col items-center justify-center space-y-1 sm:space-y-2 p-2 sm:p-3" asChild>
                                <Link href="/inventory/receive">
                                    <ArrowUpDown className="h-5 w-5 sm:h-6 sm:w-6" />
                                    <span className="text-xs sm:text-sm font-medium text-center leading-tight">
                                        <span className="sm:hidden">Receive</span>
                                        <span className="hidden sm:inline">Receive Goods</span>
                                    </span>
                                </Link>
                            </Button>
                            <Button variant="outline" className="flex h-20 sm:h-24 flex-col items-center justify-center space-y-1 sm:space-y-2 p-2 sm:p-3" asChild>
                                <Link href="/inventory/adjust">
                                    <Settings className="h-5 w-5 sm:h-6 sm:w-6" />
                                    <span className="text-xs sm:text-sm font-medium text-center leading-tight">
                                        <span className="sm:hidden">Adjust</span>
                                        <span className="hidden sm:inline">Stock Adjustment</span>
                                    </span>
                                </Link>
                            </Button>
                            <Button variant="outline" className="flex h-20 sm:h-24 flex-col items-center justify-center space-y-1 sm:space-y-2 p-2 sm:p-3" asChild>
                                <Link href="/reports/inventory">
                                    <FileText className="h-5 w-5 sm:h-6 sm:w-6" />
                                    <span className="text-xs sm:text-sm font-medium text-center leading-tight">Reports</span>
                                </Link>
                            </Button>
                            <Button variant="outline" className="flex h-20 sm:h-24 flex-col items-center justify-center space-y-1 sm:space-y-2 p-2 sm:p-3" asChild>
                                <Link href="/store-requisitions/approvals">
                                    <ClipboardCheck className="h-5 w-5 sm:h-6 sm:w-6" />
                                    <span className="text-xs sm:text-sm font-medium text-center leading-tight">
                                        <span className="sm:hidden">Approvals</span>
                                        <span className="hidden sm:inline">Review Approvals</span>
                                    </span>
                                </Link>
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
