import { Head } from '@inertiajs/react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { PageProps } from '@inertiajs/core';
import { Link, usePage, router } from '@inertiajs/react';
import { ArrowLeft, Package, User, Calendar, FileText, Send, CheckCircle, XCircle} from "lucide-react";
import { useState, useMemo } from 'react';
import AppLayout from '@/layouts/app-layout';
import { StoreRequisition, StoreRequisitionItem, InventoryItem, StoreRequisitionHistoryEntry } from '@/types/store-requisitions';
import { StoreRequisitionStatusBadgeWithIcon } from '@/components/StoreRequisitionStatusBadge';
import { canEditStoreRequisition, getStoreRequisitionEditRoute } from '@/utils/store-requisitions';
import { ApprovalDialog } from './ApprovalDialog';
import { useStoreRequisitionApproval } from '@/hooks/use-store-requisition-approval';
import { StoreRequisitionHistory } from '@/components/StoreRequisitions/StoreRequisitionHistory';

/**
 * Helper function to get inventory item from store requisition item.
 * Handles both camelCase (inventoryItem) and snake_case (inventory_item) naming conventions
 * that may come from the backend.
 *
 * @param item - The store requisition item
 * @returns The inventory item or undefined if not found
 */
const getInventoryItem = (item: StoreRequisitionItem): InventoryItem | undefined => {
    return item.inventoryItem || (item as StoreRequisitionItem & { inventory_item?: InventoryItem }).inventory_item;
};

interface ShowStoreRequisitionPageProps extends PageProps {
    store_requisition: StoreRequisition;
    histories: StoreRequisitionHistoryEntry[];
    user: {
        id: number;
        name: string;
        email: string;
        permissions: string[];
        roles?: string[];
    };
}

// Status icon logic moved to StoreRequisitionStatusBadgeWithIcon component

export default function ShowStoreRequisition() {
    const {
        store_requisition,
        histories,
        user
    } = usePage<ShowStoreRequisitionPageProps>().props;



    const breadcrumbs = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Store Requisitions',
            href: '/store-requisitions',
        },
        {
            title: `Requisition #${store_requisition.id}`,
            href: `/store-requisitions/${store_requisition.id}`,
        },
    ];

    const canEdit = canEditStoreRequisition(store_requisition, user.id);
    const canSubmit = store_requisition.status === 'draft' && store_requisition.requester_user_id === user.id;
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Approval functionality
    const [selectedRequisition, setSelectedRequisition] = useState<StoreRequisition | null>(null);
    const [dialogAction, setDialogAction] = useState<'approve' | 'reject' | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const { approveRequisition, rejectRequisition, isSubmitting: isApprovalSubmitting } = useStoreRequisitionApproval({
        onSuccess: () => {
            setIsDialogOpen(false);
            setSelectedRequisition(null);
            setDialogAction(null);
            // Refresh the page to show updated status
            router.reload();
        }
    });

    // Check if user can approve this requisition using granular role-based logic
    const canApprove = useMemo(() => {
        if (store_requisition.status !== 'pending_approval' || store_requisition.requester_user_id === user.id) {
            return false;
        }

        // Safely check if requester is a store keeper
        const requesterPermissions = store_requisition.requester?.permissions;
        const requesterIsStoreKeeper = Array.isArray(requesterPermissions)
            ? requesterPermissions.includes('store-keep')
            : false;

        if (requesterIsStoreKeeper) {
            // Store keeper requisitions can only be approved by Finance Manager or Organization Admin
            return user.roles?.includes('Finance Manager') || user.roles?.includes('Organization Admin');
        } else {
            // Employee/HOD requisitions can only be approved by store keepers
            return user.permissions.includes('store-keep');
        }
    }, [store_requisition, user]);

    const openApprovalDialog = () => {
        setSelectedRequisition(store_requisition);
        setDialogAction('approve');
        setIsDialogOpen(true);
    };

    const openRejectionDialog = () => {
        setSelectedRequisition(store_requisition);
        setDialogAction('reject');
        setIsDialogOpen(true);
    };

    const handleApprovalSubmit = (comments?: string) => {
        if (!selectedRequisition || !dialogAction) return;

        if (dialogAction === 'approve') {
            approveRequisition(selectedRequisition.id, comments);
        } else if (dialogAction === 'reject') {
            rejectRequisition(selectedRequisition.id, comments || '');
        }
    };

    const handleSubmit = () => {
        if (isSubmitting) return;

        setIsSubmitting(true);
        router.post(`/store-requisitions/${store_requisition.id}/submit`, {}, {
            onSuccess: () => {
                setIsSubmitting(false);
            },
            onError: () => {
                setIsSubmitting(false);
            }
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Store Requisition #${store_requisition.id}`} />
            
            <div className="bg-background/90 flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="outline" size="sm" asChild>
                            <Link href="/store-requisitions">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Store Requisitions
                            </Link>
                        </Button>
                        <div>
                            <h1 className="text-2xl font-bold text-foreground">
                                Store Requisition #{store_requisition.id}
                            </h1>
                            <p className="text-muted-foreground">
                                View store requisition details and status
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <StoreRequisitionStatusBadgeWithIcon
                            status={store_requisition.status}
                            showIcon={false}
                        />
                        {canSubmit && (
                            <Button
                                onClick={handleSubmit}
                                disabled={isSubmitting}
                                className="gap-2"
                            >
                                <Send className="h-4 w-4" />
                                {isSubmitting ? 'Submitting...' : 'Submit for Approval'}
                            </Button>
                        )}
                        {canEdit && (
                            <Button asChild variant="outline">
                                <Link href={getStoreRequisitionEditRoute(store_requisition)}>
                                    Edit
                                </Link>
                            </Button>
                        )}
                        {canApprove && (
                            <>
                                <Button
                                    onClick={openApprovalDialog}
                                    disabled={isApprovalSubmitting}
                                    className="gap-2"
                                >
                                    <CheckCircle className="h-4 w-4" />
                                    Approve
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={openRejectionDialog}
                                    disabled={isApprovalSubmitting}
                                    className="gap-2"
                                >
                                    <XCircle className="h-4 w-4" />
                                    Reject
                                </Button>
                            </>
                        )}
                    </div>
                </div>

                <Tabs defaultValue="details" className="space-y-4">
                    <TabsList>
                        <TabsTrigger value="details">Requisition Details</TabsTrigger>
                        {histories.length > 0 && (
                            <TabsTrigger value="history">History</TabsTrigger>
                        )}
                    </TabsList>

                    <TabsContent value="details" className="space-y-4">
                        <div className="grid gap-6 md:grid-cols-2">
                            {/* Requisition Details */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <FileText className="h-5 w-5" />
                                        Requisition Details
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Purpose</label>
                                        <p className="mt-1">{store_requisition.purpose}</p>
                                    </div>

                                    <Separator />

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Department</label>
                                            <p className="mt-1">{store_requisition.department?.name}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Branch</label>
                                            <p className="mt-1">{store_requisition.branch?.name}</p>
                                        </div>
                                    </div>

                                    <Separator />

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Requested Date</label>
                                            <p className="mt-1 flex items-center gap-2">
                                                <Calendar className="h-4 w-4" />
                                                {store_requisition.requested_at ? new Date(store_requisition.requested_at).toLocaleDateString() : 'Not requested'}
                                            </p>
                                        </div>
                                        {store_requisition.approved_at && (
                                            <div>
                                                <label className="text-sm font-medium text-muted-foreground">Approved Date</label>
                                                <p className="mt-1 flex items-center gap-2">
                                                    <Calendar className="h-4 w-4" />
                                                    {new Date(store_requisition.approved_at).toLocaleDateString()}
                                                </p>
                                            </div>
                                        )}
                                    </div>

                                    {store_requisition.issued_at && (
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Issued Date</label>
                                            <p className="mt-1 flex items-center gap-2">
                                                <Calendar className="h-4 w-4" />
                                                {new Date(store_requisition.issued_at).toLocaleDateString()}
                                            </p>
                                        </div>
                                    )}

                                    {store_requisition.rejection_reason && (
                                        <>
                                            <Separator />
                                            <div>
                                                <label className="text-sm font-medium text-muted-foreground">Rejection Reason</label>
                                                <p className="mt-1 text-destructive">{store_requisition.rejection_reason}</p>
                                            </div>
                                        </>
                                    )}
                                </CardContent>
                            </Card>

                            {/* People Involved */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <User className="h-5 w-5" />
                                        People Involved
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Requester</label>
                                        <p className="mt-1">{store_requisition.requester?.first_name} {store_requisition.requester?.last_name}</p>
                                        <p className="text-sm text-muted-foreground">{store_requisition.requester?.email}</p>
                                    </div>

                                    {store_requisition.approver && (
                                        <>
                                            <Separator />
                                            <div>
                                                <label className="text-sm font-medium text-muted-foreground">Approver</label>
                                                <p className="mt-1">{store_requisition.approver.first_name} {store_requisition.approver.last_name}</p>
                                                <p className="text-sm text-muted-foreground">{store_requisition.approver.email}</p>
                                            </div>
                                        </>
                                    )}

                                    {store_requisition.issuer && (
                                        <>
                                            <Separator />
                                            <div>
                                                <label className="text-sm font-medium text-muted-foreground">Issuer</label>
                                                <p className="mt-1">{store_requisition.issuer.first_name} {store_requisition.issuer.last_name}</p>
                                                <p className="text-sm text-muted-foreground">{store_requisition.issuer.email}</p>
                                            </div>
                                        </>
                                    )}
                                </CardContent>
                            </Card>
                        </div>

                        {/* Requested Items */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Package className="h-5 w-5" />
                                    Requested Items ({store_requisition.items?.length || 0})
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                {store_requisition.items && store_requisition.items.length > 0 ? (
                                    <div className="space-y-4">
                                        {store_requisition.items.map((item, index) => {
                                            const inventoryItem = getInventoryItem(item);
                                            return (
                                                <div key={index} className="border rounded-lg p-4">
                                                    <div className="grid gap-4 md:grid-cols-4">
                                                        <div className="md:col-span-2">
                                                            <label className="text-sm font-medium text-muted-foreground">Item</label>
                                                            <p className="mt-1 font-medium">{inventoryItem?.name}</p>
                                                            {inventoryItem?.description && (
                                                                <p className="text-sm text-muted-foreground">{inventoryItem.description}</p>
                                                            )}
                                                            <p className="text-xs text-muted-foreground">SKU: {inventoryItem?.sku}</p>
                                                        </div>
                                                        <div>
                                                            <label className="text-sm font-medium text-muted-foreground">Quantity Requested</label>
                                                            <p className="mt-1">{item.quantity_requested} {inventoryItem?.unit_of_measure}</p>
                                                        </div>
                                                        {item.quantity_issued !== undefined && (
                                                            <div>
                                                                <label className="text-sm font-medium text-muted-foreground">Quantity Issued</label>
                                                                <p className="mt-1">{item.quantity_issued} {inventoryItem?.unit_of_measure}</p>
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                ) : (
                                    <p className="text-muted-foreground text-center py-8">No items found</p>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="history" className="space-y-4">
                        <StoreRequisitionHistory histories={histories} />
                    </TabsContent>
                </Tabs>
            </div>

            {/* Approval Dialog */}
            {selectedRequisition && dialogAction && (
                <ApprovalDialog
                    requisition={selectedRequisition}
                    action={dialogAction}
                    isOpen={isDialogOpen}
                    onClose={() => setIsDialogOpen(false)}
                    onSubmit={handleApprovalSubmit}
                    isSubmitting={isApprovalSubmitting}
                />
            )}
        </AppLayout>
    );
}
