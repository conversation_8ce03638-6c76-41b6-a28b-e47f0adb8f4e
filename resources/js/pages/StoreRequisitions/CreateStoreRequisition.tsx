import { <PERSON><PERSON>ventHandler } from 'react';
import { Head } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { PageProps } from '@inertiajs/core';
import { router, usePage } from '@inertiajs/react';
import { LoaderCircle } from "lucide-react";
import AppLayout from '@/layouts/app-layout';
import StoreRequisitionItemsManager from '@/components/StoreRequisitions/StoreRequisitionItemsManager';
import { useStoreRequisitionForm } from '@/hooks/use-store-requisition-form';
import { InventoryItem, StoreRequisitionItemFormData } from '@/types/store-requisitions';

type Department = {
    id: number;
    name: string;
    branch_id: number;
    organization_id: number;
};

interface CreateStoreRequisitionPageProps extends PageProps {
    inventory_items: InventoryItem[];
    user_branch_id?: number;
    user_department_id?: number;
    user_departments: Department[];
    department_id?: number;
    department_name?: string;
}

const CreateStoreRequisition = () => {
    const {
        inventory_items = [],
        user_branch_id,
        user_department_id,
        user_departments = [],
        department_id,
        department_name,
    } = usePage<CreateStoreRequisitionPageProps>().props;

    // Function to handle department change (similar to petty cash requisition)
    const handleDepartmentChange = (departmentId: string) => {
        // Use Inertia router to navigate to the same page with a different department parameter
        router.get('/store-requisitions/create', { department: departmentId });
    };

    // Determine the correct branch and department IDs
    const selectedDepartment = department_id
        ? user_departments.find(d => d.id === department_id)
        : user_departments.find(d => d.id === user_department_id) || user_departments[0];

    const initialBranchId = selectedDepartment?.branch_id || user_branch_id || (user_departments[0]?.branch_id);
    const initialDepartmentId = selectedDepartment?.id || user_department_id || (user_departments[0]?.id);

    // Initialize the store requisition form hook
    const {
        data,
        setData,
        items,
        addItem,
        removeItem,
        updateItem,
        errors,
        processing,
        submit,
        submitAsDraft,
    } = useStoreRequisitionForm({
        initialData: {
            branch_id: initialBranchId,
            department_id: initialDepartmentId,
            purpose: '',
            items: [{ inventory_item_id: null, quantity_requested: 1 }]
        },
        onSuccess: () => {
            // Success message will be handled by the server redirect
            // No need to show toast or redirect here since server handles it
        },
        onError: (errors) => {

            // Extract error messages
            const errorMessages: string[] = [];
            if (typeof errors === 'object' && errors !== null) {
                for (const [key, value] of Object.entries(errors)) {
                    if (typeof value === 'string') {
                        errorMessages.push(`${key}: ${value}`);
                    } else if (Array.isArray(value)) {
                        errorMessages.push(`${key}: ${(value as string[]).join(', ')}`);
                    }
                }
            }

            const message = errorMessages.length > 0
                ? errorMessages.join('\n')
                : 'Submission failed. Please check the form and try again.';

            window.showToast?.({
                title: 'Validation Error',
                message: message,
                type: 'error'
            });
        }
    });



    // Handle item changes using the hook's updateItem function
    const handleItemChange = (index: number, field: keyof StoreRequisitionItemFormData, value: string) => {
        if (field === 'quantity_requested') {
            // Convert to number, default to 1 if invalid
            const numValue = parseFloat(value) || 1;
            updateItem(index, field, numValue);
        } else if (field === 'inventory_item_id') {
            updateItem(index, field, value ? Number(value) : null);
        }
    };

    // Handle remove item with toast notification
    const handleRemoveItem = (index: number) => {
        if (items.length === 1) {
            window.showToast?.({
                title: 'Validation Error',
                message: 'At least one item required',
                type: 'error'
            });
            return;
        }
        removeItem(index);
    };

    const handleSubmit: FormEventHandler = (e) => {
        e.preventDefault();
        submit('/store-requisitions');
    };

    const handleSaveAsDraft = () => {
        submitAsDraft('/store-requisitions');
    };



    const breadcrumbs = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Store Requisitions',
            href: '/store-requisitions',
        },
        {
            title: 'Create Store Requisition',
            href: '/store-requisitions/create',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Store Requisition" />

            {/* Department Information Card - matching petty cash pattern */}
            <Card className="w-full max-w-4xl mx-auto mb-4">
                <CardHeader>
                    <CardTitle className="text-2xl">Department Information</CardTitle>
                    <CardDescription>
                        This store requisition will be created for the following department
                    </CardDescription>
                </CardHeader>
                <CardContent className="pt-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-muted-foreground">Department:</p>
                            <p className="text-lg font-medium">{department_name || 'Not selected'}</p>
                        </div>

                        {user_departments.length > 1 && (
                            <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
                                <Badge variant="secondary" className="w-fit">
                                    {user_departments.length} departments available
                                </Badge>
                                <Select value={department_id?.toString() || ''} onValueChange={handleDepartmentChange}>
                                    <SelectTrigger className="w-full sm:w-[200px]">
                                        <SelectValue placeholder="Switch department" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {user_departments.map((dept) => (
                                            <SelectItem key={dept.id} value={dept.id.toString()}>
                                                {dept.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        )}
                    </div>
                </CardContent>
            </Card>

            {/* Main Form Card - matching petty cash pattern */}
            <Card className="mx-auto w-full max-w-4xl">
                <CardHeader>
                    <CardTitle className="text-2xl">Store Requisition Form</CardTitle>
                    <CardDescription>
                        Complete the form below to submit a new store requisition request
                    </CardDescription>
                </CardHeader>

                <form onSubmit={handleSubmit}>
                    <CardContent className="space-y-6 pt-6">
                        <div className="space-y-2">
                            <Label htmlFor="purpose" className="text-base">
                                Purpose <span className="text-destructive">*</span>
                            </Label>
                            <Textarea
                                id="purpose"
                                value={data.purpose}
                                onChange={(e) => setData('purpose', e.target.value)}
                                placeholder="Briefly describe the purpose of this store requisition"
                                className={errors.purpose ? 'border-destructive' : ''}
                                rows={3}
                                autoFocus
                            />
                            {errors.purpose && <p className="text-sm text-destructive">{errors.purpose}</p>}
                        </div>

                        <div className="pt-6">
                            <h3 className="mb-4 text-lg font-medium">Items</h3>
                            <StoreRequisitionItemsManager
                                items={items}
                                inventoryItems={inventory_items}
                                errors={errors}
                                onItemChange={handleItemChange}
                                onAddItem={addItem}
                                onRemoveItem={handleRemoveItem}
                            />
                        </div>
                    </CardContent>

                    <CardFooter className="flex justify-between pt-6">
                        <Button
                            type="button"
                            variant="outline"
                            disabled={processing}
                            onClick={() => router.visit('/store-requisitions')}
                            className="hover:bg-muted hover:text-foreground transition-colors"
                        >
                            Cancel
                        </Button>
                        <div className="flex gap-2">
                            <Button
                                type="button"
                                variant="secondary"
                                onClick={handleSaveAsDraft}
                                disabled={processing}
                                className="hover:bg-muted hover:text-foreground transition-colors"
                            >
                                {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                                Save as Draft
                            </Button>
                            <Button type="submit" disabled={processing}>
                                {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                                Submit Store Requisition
                            </Button>
                        </div>
                    </CardFooter>
                </form>
            </Card>
        </AppLayout>
    );
};

export default CreateStoreRequisition;
