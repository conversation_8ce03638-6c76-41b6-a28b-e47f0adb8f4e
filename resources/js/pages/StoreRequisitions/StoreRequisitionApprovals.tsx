import React, { useState, useEffect } from 'react';
import { Head, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
    CheckCircle,
    XCircle,
    Search,
    Filter,
    ArrowLeft,
    Package
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { StoreRequisition } from '@/types/store-requisitions';
import { StoreRequisitionStatusBadgeWithIcon } from '@/components/StoreRequisitionStatusBadge';
import { ApprovalDialog } from './ApprovalDialog';
import { useStoreRequisitionApproval } from '@/hooks/use-store-requisition-approval';
import { PageProps } from '@inertiajs/core';

interface StoreRequisitionApprovalsPageProps extends PageProps {
    pending_approvals: StoreRequisition[];
    user: {
        id: number;
        name: string;
        email: string;
        permissions?: string[];
    };
    flash?: {
        success?: string;
        error?: string;
    };
    [key: string]: unknown; // Index signature to satisfy PageProps constraint
}

export default function StoreRequisitionApprovals() {
    const { pending_approvals } = usePage<StoreRequisitionApprovalsPageProps>().props;
    
    const [selectedRequisition, setSelectedRequisition] = useState<StoreRequisition | null>(null);
    const [dialogAction, setDialogAction] = useState<'approve' | 'reject' | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [departmentFilter, setDepartmentFilter] = useState('all');

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    // Reset pagination when filters change
    useEffect(() => {
        setCurrentPage(1);
    }, [searchTerm, statusFilter, departmentFilter]);

    const { approveRequisition, rejectRequisition, isSubmitting } = useStoreRequisitionApproval({
        onSuccess: () => {
            setIsDialogOpen(false);
            setSelectedRequisition(null);
            setDialogAction(null);
        }
    });



    // Filter requisitions based on search and filters
    const allFilteredRequisitions = pending_approvals.filter(requisition => {
        const matchesSearch = searchTerm === '' ||
            requisition.purpose.toLowerCase().includes(searchTerm.toLowerCase()) ||
            requisition.id.toString().includes(searchTerm) ||
            (requisition.requester?.first_name + ' ' + requisition.requester?.last_name)
                .toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStatus = statusFilter === 'all' || requisition.status === statusFilter;

        const matchesDepartment = departmentFilter === 'all' ||
            requisition.department?.id.toString() === departmentFilter;

        return matchesSearch && matchesStatus && matchesDepartment;
    });

    // Apply pagination
    const totalCount = allFilteredRequisitions.length;
    const totalPages = Math.ceil(totalCount / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const filteredRequisitions = allFilteredRequisitions.slice(startIndex, endIndex);

    // Get unique departments for filter
    const departments = pending_approvals
        .map(req => req.department)
        .filter(Boolean)
        .reduce((unique: typeof pending_approvals[0]['department'][], dept) => {
            if (!unique.find(d => d!.id === dept!.id)) {
                unique.push(dept);
            }
            return unique;
        }, []);

    const openApprovalDialog = (requisition: StoreRequisition) => {
        setSelectedRequisition(requisition);
        setDialogAction('approve');
        setIsDialogOpen(true);
    };

    const openRejectionDialog = (requisition: StoreRequisition) => {
        setSelectedRequisition(requisition);
        setDialogAction('reject');
        setIsDialogOpen(true);
    };

    const handleApprovalSubmit = (comments?: string) => {
        if (!selectedRequisition) return;
        
        if (dialogAction === 'approve') {
            approveRequisition(selectedRequisition.id, comments);
        } else if (dialogAction === 'reject' && comments) {
            rejectRequisition(selectedRequisition.id, comments);
        }
    };

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Store Requisition Approvals', href: '/store-requisitions/approvals' },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Store Requisition Approvals" />

            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="outline" asChild>
                            <a href="/dashboard">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Dashboard
                            </a>
                        </Button>
                        <div>
                            <h1 className="text-2xl font-bold text-foreground">
                                Store Requisition Approvals
                            </h1>
                            <p className="text-muted-foreground">
                                Review and approve pending store requisitions
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-sm">
                            {filteredRequisitions.length} Pending
                        </Badge>
                    </div>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filters
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Search by purpose, ID, or requester..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                            
                            <Select value={statusFilter} onValueChange={setStatusFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Filter by status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Statuses</SelectItem>
                                    <SelectItem value="pending_approval">Pending Approval</SelectItem>
                                </SelectContent>
                            </Select>

                            <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Filter by department" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Departments</SelectItem>
                                    {departments.map((dept) => (
                                        <SelectItem key={dept!.id} value={dept!.id.toString()}>
                                            {dept!.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </CardContent>
                </Card>

                {/* Requisitions List */}
                {totalCount === 0 ? (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <Package className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold text-muted-foreground mb-2">
                                No Pending Approvals
                            </h3>
                            <p className="text-muted-foreground text-center">
                                {searchTerm || statusFilter !== 'all' || departmentFilter !== 'all'
                                    ? 'No requisitions match your current filters.'
                                    : 'There are no store requisitions pending your approval at this time.'
                                }
                            </p>
                        </CardContent>
                    </Card>
                ) : (
                    <Card>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Requisition ID</TableHead>
                                    <TableHead>Requester</TableHead>
                                    <TableHead>Department</TableHead>
                                    <TableHead>Purpose</TableHead>
                                    <TableHead>Items Count</TableHead>
                                    <TableHead>Requested Date</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead className="text-right">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {filteredRequisitions.map((requisition) => (
                                    <TableRow key={requisition.id} className="hover:bg-muted/50">
                                        <TableCell className="font-medium">
                                            #{requisition.id}
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex flex-col">
                                                <span className="font-medium">
                                                    {requisition.requester?.first_name} {requisition.requester?.last_name}
                                                </span>
                                                <span className="text-sm text-muted-foreground">
                                                    {requisition.requester?.email}
                                                </span>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            {requisition.department?.name || 'N/A'}
                                        </TableCell>
                                        <TableCell className="max-w-xs">
                                            <div className="truncate" title={requisition.purpose}>
                                                {requisition.purpose}
                                            </div>
                                        </TableCell>
                                        <TableCell className="text-center">
                                            {requisition.items?.length || 0}
                                        </TableCell>
                                        <TableCell>
                                            {requisition.requested_at
                                                ? new Date(requisition.requested_at).toLocaleDateString()
                                                : 'N/A'
                                            }
                                        </TableCell>
                                        <TableCell>
                                            <StoreRequisitionStatusBadgeWithIcon
                                                status={requisition.status}
                                                showIcon={false}
                                            />
                                        </TableCell>
                                        <TableCell className="text-right">
                                            <div className="flex gap-2 justify-end">
                                                <Button
                                                    onClick={() => openApprovalDialog(requisition)}
                                                    size="sm"
                                                    className="gap-2"
                                                >
                                                    <CheckCircle className="h-4 w-4" />
                                                    Approve
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    onClick={() => openRejectionDialog(requisition)}
                                                    size="sm"
                                                    className="gap-2"
                                                >
                                                    <XCircle className="h-4 w-4" />
                                                    Reject
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>

                        {/* Pagination Controls */}
                        {totalPages > 1 && (
                            <div className="flex items-center justify-between px-4 py-3 border-t">
                                <div className="text-sm text-muted-foreground">
                                    Showing {startIndex + 1} to {Math.min(endIndex, totalCount)} of {totalCount} requisitions
                                </div>
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                        disabled={currentPage === 1}
                                    >
                                        Previous
                                    </Button>
                                    <span className="text-sm text-muted-foreground">
                                        Page {currentPage} of {totalPages}
                                    </span>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                                        disabled={currentPage === totalPages}
                                    >
                                        Next
                                    </Button>
                                </div>
                            </div>
                        )}
                    </Card>
                )}

                {/* Approval Dialog */}
            {selectedRequisition && dialogAction && (
                <ApprovalDialog
                    requisition={selectedRequisition}
                    action={dialogAction}
                    isOpen={isDialogOpen}
                    onClose={() => {
                        setIsDialogOpen(false);
                        setSelectedRequisition(null);
                        setDialogAction(null);
                    }}
                    onSubmit={handleApprovalSubmit}
                    isSubmitting={isSubmitting}
                />
            )}
            </div>
        </AppLayout>
    );
}
